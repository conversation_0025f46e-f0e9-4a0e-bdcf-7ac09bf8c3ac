set check_function_bodies = on;

CREATE FUNCTION sp_fetch_user_campaigns(
  _user_id UUID,
  _campaign_id UUID DEFAULT NULL
)

RETURNS TABLE(
  id UUID,
  name TEXT,
  description TEXT,
  cover_image TEXT,
  created_by <PERSON><PERSON><PERSON>,
  short_code TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  role TEXT
) AS $$

BEGIN
    RETURN QUERY
    SELECT
      c.id,
      c.name,
      c.description,
      c.cover_image,
      c.created_by,
      c.short_code,
      c.is_active,
      c.created_at,
      c.updated_at,
      cp.role
    FROM t_campaign c
    JOIN t_campaign_has_player cp ON c.id = cp.campaign_id
    WHERE cp.player_id = _user_id AND (_campaign_id IS NULL OR c.id = _campaign_id)
    ORDER BY c.created_at ASC;
END;

$$ LANGUAGE PLPGSQL SECURITY DEFINER;
