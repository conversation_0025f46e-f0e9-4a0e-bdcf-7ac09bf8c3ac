
  create table "public"."t_campaign" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "description" text,
    "cover_image" text,
    "created_by" uuid,
    "is_active" boolean default true,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
      );



  create table "public"."t_campaign_has_player" (
    "campaign_id" uuid not null,
    "player_id" uuid not null,
    "role" text not null,
    "joined_at" timestamp with time zone default now()
      );


CREATE INDEX idx_campaign_name ON public.t_campaign USING btree (name);

CREATE UNIQUE INDEX t_campaign_has_player_pkey ON public.t_campaign_has_player USING btree (campaign_id, player_id);

CREATE UNIQUE INDEX t_campaign_pkey ON public.t_campaign USING btree (id);

alter table "public"."t_campaign" add constraint "t_campaign_pkey" PRIMARY KEY using index "t_campaign_pkey";

alter table "public"."t_campaign_has_player" add constraint "t_campaign_has_player_pkey" PRIMARY KEY using index "t_campaign_has_player_pkey";

alter table "public"."t_campaign" add constraint "t_campaign_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE RESTRICT not valid;

alter table "public"."t_campaign" validate constraint "t_campaign_created_by_fkey";

alter table "public"."t_campaign_has_player" add constraint "t_campaign_has_player_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES t_campaign(id) ON DELETE CASCADE not valid;

alter table "public"."t_campaign_has_player" validate constraint "t_campaign_has_player_campaign_id_fkey";

alter table "public"."t_campaign_has_player" add constraint "t_campaign_has_player_player_id_fkey" FOREIGN KEY (player_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."t_campaign_has_player" validate constraint "t_campaign_has_player_player_id_fkey";

alter table "public"."t_campaign_has_player" add constraint "t_campaign_has_player_role_check" CHECK ((role = ANY (ARRAY['Player'::text, 'DM'::text]))) not valid;

alter table "public"."t_campaign_has_player" validate constraint "t_campaign_has_player_role_check";

grant delete on table "public"."t_campaign" to "anon";

grant insert on table "public"."t_campaign" to "anon";

grant references on table "public"."t_campaign" to "anon";

grant select on table "public"."t_campaign" to "anon";

grant trigger on table "public"."t_campaign" to "anon";

grant truncate on table "public"."t_campaign" to "anon";

grant update on table "public"."t_campaign" to "anon";

grant delete on table "public"."t_campaign" to "authenticated";

grant insert on table "public"."t_campaign" to "authenticated";

grant references on table "public"."t_campaign" to "authenticated";

grant select on table "public"."t_campaign" to "authenticated";

grant trigger on table "public"."t_campaign" to "authenticated";

grant truncate on table "public"."t_campaign" to "authenticated";

grant update on table "public"."t_campaign" to "authenticated";

grant delete on table "public"."t_campaign" to "service_role";

grant insert on table "public"."t_campaign" to "service_role";

grant references on table "public"."t_campaign" to "service_role";

grant select on table "public"."t_campaign" to "service_role";

grant trigger on table "public"."t_campaign" to "service_role";

grant truncate on table "public"."t_campaign" to "service_role";

grant update on table "public"."t_campaign" to "service_role";

grant delete on table "public"."t_campaign_has_player" to "anon";

grant insert on table "public"."t_campaign_has_player" to "anon";

grant references on table "public"."t_campaign_has_player" to "anon";

grant select on table "public"."t_campaign_has_player" to "anon";

grant trigger on table "public"."t_campaign_has_player" to "anon";

grant truncate on table "public"."t_campaign_has_player" to "anon";

grant update on table "public"."t_campaign_has_player" to "anon";

grant delete on table "public"."t_campaign_has_player" to "authenticated";

grant insert on table "public"."t_campaign_has_player" to "authenticated";

grant references on table "public"."t_campaign_has_player" to "authenticated";

grant select on table "public"."t_campaign_has_player" to "authenticated";

grant trigger on table "public"."t_campaign_has_player" to "authenticated";

grant truncate on table "public"."t_campaign_has_player" to "authenticated";

grant update on table "public"."t_campaign_has_player" to "authenticated";

grant delete on table "public"."t_campaign_has_player" to "service_role";

grant insert on table "public"."t_campaign_has_player" to "service_role";

grant references on table "public"."t_campaign_has_player" to "service_role";

grant select on table "public"."t_campaign_has_player" to "service_role";

grant trigger on table "public"."t_campaign_has_player" to "service_role";

grant truncate on table "public"."t_campaign_has_player" to "service_role";

grant update on table "public"."t_campaign_has_player" to "service_role";


