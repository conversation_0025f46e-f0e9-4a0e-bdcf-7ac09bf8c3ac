CREATE TABLE t_block (
    id UUID NOT NULL DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT,
    block_type UUID REFERENCES t_block_type(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    note_id UUID REFERENCES t_note(id) ON DELETE CASCADE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL DEFAULT NULL
);

CREATE INDEX idx_block_type_title ON public.t_block USING btree (title);

CREATE UNIQUE INDEX t_block_pkey ON public.t_block USING btree (id);

ALTER TABLE "public"."t_block" ADD CONSTRAINT "t_block_pkey" PRIMARY KEY USING INDEX "t_block_pkey";

GRANT DELETE ON TABLE t_block TO anon;
GRANT INSERT ON TABLE t_block TO anon;
GRANT REFERENCES ON TABLE t_block TO anon;
GRANT SELECT ON TABLE t_block TO anon;
GRANT TRIGGER ON TABLE t_block TO anon;
GRANT TRUNCATE ON TABLE t_block TO anon;
GRANT UPDATE ON TABLE t_block TO anon;
GRANT DELETE ON TABLE t_block TO authenticated;
GRANT INSERT ON TABLE t_block TO authenticated;
GRANT REFERENCES ON TABLE t_block TO authenticated;
GRANT SELECT ON TABLE t_block TO authenticated;
GRANT TRIGGER ON TABLE t_block TO authenticated;
GRANT TRUNCATE ON TABLE t_block TO authenticated;
GRANT UPDATE ON TABLE t_block TO authenticated;
GRANT DELETE ON TABLE t_block TO service_role;
GRANT INSERT ON TABLE t_block TO service_role;
GRANT REFERENCES ON TABLE t_block TO service_role;
GRANT SELECT ON TABLE t_block TO service_role;
GRANT TRIGGER ON TABLE t_block TO service_role;
GRANT TRUNCATE ON TABLE t_block TO service_role;
GRANT UPDATE ON TABLE t_block TO service_role;
