
  create table "public"."t_note" (
    "id" uuid not null default gen_random_uuid(),
    "title" text not null,
    "content" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "folder_id" uuid,
    "created_by" uuid,
    "updated_by" uuid,
    "campaign_id" uuid
      );


CREATE INDEX idx_note_campaign_id ON public.t_note USING btree (campaign_id);

CREATE INDEX idx_note_created_by ON public.t_note USING btree (created_by);

CREATE INDEX idx_note_folder_id ON public.t_note USING btree (folder_id);

CREATE INDEX idx_note_updated_by ON public.t_note USING btree (updated_by);

CREATE UNIQUE INDEX t_note_pkey ON public.t_note USING btree (id);

alter table "public"."t_note" add constraint "t_note_pkey" PRIMARY KEY using index "t_note_pkey";

alter table "public"."t_note" add constraint "t_note_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES t_campaign(id) ON DELETE CASCADE not valid;

alter table "public"."t_note" validate constraint "t_note_campaign_id_fkey";

alter table "public"."t_note" add constraint "t_note_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."t_note" validate constraint "t_note_created_by_fkey";

alter table "public"."t_note" add constraint "t_note_folder_id_fkey" FOREIGN KEY (folder_id) REFERENCES t_folder(id) ON DELETE SET NULL not valid;

alter table "public"."t_note" validate constraint "t_note_folder_id_fkey";

alter table "public"."t_note" add constraint "t_note_updated_by_fkey" FOREIGN KEY (updated_by) REFERENCES auth.users(id) ON DELETE SET NULL not valid;

alter table "public"."t_note" validate constraint "t_note_updated_by_fkey";

set check_function_bodies = on;

CREATE FUNCTION sp_fetch_folders(
  _campaign_id UUID,
  _user_id UUID DEFAULT NULL,
  _include_notes BOOLEAN DEFAULT TRUE,
  _include_deleted BOOLEAN DEFAULT FALSE
)

RETURNS TABLE(
  id UUID,
  name TEXT,
  created_by UUID,
  campaign_id UUID,
  icon TEXT,
  color TEXT,
  is_deleted BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  notes JSONB
) AS $$


BEGIN
  RETURN QUERY
  SELECT
    f.id,
    f.name,
    f.created_by,
    f.campaign_id,
    f.icon,
    f.color,
    f.is_deleted,
    f.created_at,
    f.updated_at,
    CASE
      WHEN _include_notes THEN (
        SELECT COALESCE(JSONB_AGG(
          JSONB_BUILD_OBJECT(
            'id', n.id,
            'title', n.title,
            'content', n.content,
            'created_at', n.created_at,
            'updated_at', n.updated_at,
            'folder_id', n.folder_id,
            'created_by', n.created_by,
            'updated_by', n.updated_by,
            'campaign_id', n.campaign_id
          )
        ) FILTER (WHERE n.id IS NOT NULL), '[]'::JSONB)
        FROM t_note n
        WHERE n.folder_id = f.id
      )
      ELSE '[]'::JSONB
    END AS notes
  FROM t_folder f
  WHERE f.campaign_id = _campaign_id
    AND (_include_deleted OR f.is_deleted = FALSE)
    AND (_user_id IS NULL OR f.created_by = _user_id OR EXISTS (
      SELECT 1
      FROM t_campaign_has_player cp
      WHERE cp.campaign_id = f.campaign_id AND cp.player_id = _user_id
    ))
  ORDER BY f.created_at ASC;
END;

$$ LANGUAGE PLPGSQL SECURITY DEFINER;


grant delete on table "public"."t_note" to "anon";

grant insert on table "public"."t_note" to "anon";

grant references on table "public"."t_note" to "anon";

grant select on table "public"."t_note" to "anon";

grant trigger on table "public"."t_note" to "anon";

grant truncate on table "public"."t_note" to "anon";

grant update on table "public"."t_note" to "anon";

grant delete on table "public"."t_note" to "authenticated";

grant insert on table "public"."t_note" to "authenticated";

grant references on table "public"."t_note" to "authenticated";

grant select on table "public"."t_note" to "authenticated";

grant trigger on table "public"."t_note" to "authenticated";

grant truncate on table "public"."t_note" to "authenticated";

grant update on table "public"."t_note" to "authenticated";

grant delete on table "public"."t_note" to "service_role";

grant insert on table "public"."t_note" to "service_role";

grant references on table "public"."t_note" to "service_role";

grant select on table "public"."t_note" to "service_role";

grant trigger on table "public"."t_note" to "service_role";

grant truncate on table "public"."t_note" to "service_role";

grant update on table "public"."t_note" to "service_role";


