  create table "public"."t_block_type" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "description" text,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
      );


CREATE INDEX idx_block_type_name ON public.t_block_type USING btree (name);

CREATE UNIQUE INDEX t_block_type_pkey ON public.t_block_type USING btree (id);

alter table "public"."t_block_type" add constraint "t_block_type_pkey" PRIMARY KEY using index "t_block_type_pkey";

grant delete on table "public"."t_block_type" to "anon";

grant insert on table "public"."t_block_type" to "anon";

grant references on table "public"."t_block_type" to "anon";

grant select on table "public"."t_block_type" to "anon";

grant trigger on table "public"."t_block_type" to "anon";

grant truncate on table "public"."t_block_type" to "anon";

grant update on table "public"."t_block_type" to "anon";

grant delete on table "public"."t_block_type" to "authenticated";

grant insert on table "public"."t_block_type" to "authenticated";

grant references on table "public"."t_block_type" to "authenticated";

grant select on table "public"."t_block_type" to "authenticated";

grant trigger on table "public"."t_block_type" to "authenticated";

grant truncate on table "public"."t_block_type" to "authenticated";

grant update on table "public"."t_block_type" to "authenticated";

grant delete on table "public"."t_block_type" to "service_role";

grant insert on table "public"."t_block_type" to "service_role";

grant references on table "public"."t_block_type" to "service_role";

grant select on table "public"."t_block_type" to "service_role";

grant trigger on table "public"."t_block_type" to "service_role";

grant truncate on table "public"."t_block_type" to "service_role";

grant update on table "public"."t_block_type" to "service_role";


