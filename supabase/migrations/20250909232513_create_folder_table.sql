
  create table "public"."t_folder" (
    "id" uuid not null default gen_random_uuid(),
    "name" text not null,
    "created_by" uuid not null,
    "campaign_id" uuid not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
      );


CREATE INDEX idx_folder_campaign ON public.t_folder USING btree (campaign_id);

CREATE UNIQUE INDEX t_folder_pkey ON public.t_folder USING btree (id);

alter table "public"."t_folder" add constraint "t_folder_pkey" PRIMARY KEY using index "t_folder_pkey";

alter table "public"."t_folder" add constraint "t_folder_campaign_id_fkey" FOREIGN KEY (campaign_id) REFERENCES t_campaign(id) ON DELETE CASCADE not valid;

alter table "public"."t_folder" validate constraint "t_folder_campaign_id_fkey";

alter table "public"."t_folder" add constraint "t_folder_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."t_folder" validate constraint "t_folder_created_by_fkey";

grant delete on table "public"."t_folder" to "anon";

grant insert on table "public"."t_folder" to "anon";

grant references on table "public"."t_folder" to "anon";

grant select on table "public"."t_folder" to "anon";

grant trigger on table "public"."t_folder" to "anon";

grant truncate on table "public"."t_folder" to "anon";

grant update on table "public"."t_folder" to "anon";

grant delete on table "public"."t_folder" to "authenticated";

grant insert on table "public"."t_folder" to "authenticated";

grant references on table "public"."t_folder" to "authenticated";

grant select on table "public"."t_folder" to "authenticated";

grant trigger on table "public"."t_folder" to "authenticated";

grant truncate on table "public"."t_folder" to "authenticated";

grant update on table "public"."t_folder" to "authenticated";

grant delete on table "public"."t_folder" to "service_role";

grant insert on table "public"."t_folder" to "service_role";

grant references on table "public"."t_folder" to "service_role";

grant select on table "public"."t_folder" to "service_role";

grant trigger on table "public"."t_folder" to "service_role";

grant truncate on table "public"."t_folder" to "service_role";

grant update on table "public"."t_folder" to "service_role";


