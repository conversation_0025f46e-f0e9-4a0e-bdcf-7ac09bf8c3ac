CREATE TABLE t_campaign (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    cover_image TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE RESTRICT,
    is_active BOOLEAN DEFAULT TRUE,
    short_code TEXT UNIQUE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE INDEX idx_campaign_name ON t_campaign (name);
