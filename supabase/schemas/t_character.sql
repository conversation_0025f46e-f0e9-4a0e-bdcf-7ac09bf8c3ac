CREATE TABLE t_character (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    appearance TEXT,
    backstory TEXT,
    goals TEXT,
    created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
);

CREATE INDEX idx_character_name ON t_character(name);
CREATE INDEX idx_character_created_by ON t_character(created_by);
CREATE INDEX idx_character_deleted_at ON t_character(deleted_at);
