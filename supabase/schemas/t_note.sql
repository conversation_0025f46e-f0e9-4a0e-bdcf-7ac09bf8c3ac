CREATE TABLE t_note (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    folder_id UUID REFERENCES t_folder(id) ON DELETE SET NULL,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL DEFAULT NULL,
    campaign_id UUID REFERENCES t_campaign(id) ON DELETE CASCADE
);

CREATE INDEX idx_note_campaign_id ON t_note(campaign_id);
CREATE INDEX idx_note_folder_id ON t_note(folder_id);
CREATE INDEX idx_note_created_by ON t_note(created_by);
CREATE INDEX idx_note_updated_by ON t_note(updated_by);
