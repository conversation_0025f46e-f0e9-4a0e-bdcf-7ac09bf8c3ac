CREATE TABLE t_block (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    content TEXT,
    block_type UUID REFERENCES t_block_type(id) ON DELETE SET NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    note_id UUID REFERENCES t_note(id) ON DELETE CASCADE,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL DEFAULT NULL
);

CREATE INDEX idx_block_note_id ON t_block(note_id);
CREATE INDEX idx_block_created_by ON t_block(created_by);
CREATE INDEX idx_block_updated_by ON t_block(updated_by);
