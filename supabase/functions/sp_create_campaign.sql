CREATE FUNCTION sp_create_campaign(
  _name TEXT,
  _description TEXT,
  _created_by UUID
)

RETURNS TABLE(id UUID) AS $$

DECLARE
  _campaign_id UUID;

BEGIN
    INSERT INTO t_campaign (name, description, created_by, short_code)
    VALUES (_name, _description, _created_by, substring(md5(random()::text), 1, 8))
    RETURNING t_campaign.id INTO _campaign_id;

    INSERT INTO t_campaign_has_player (campaign_id, player_id, role)
    VALUES (_campaign_id, _created_by, 'DM');
    
    RETURN QUERY SELECT _campaign_id AS id;
END;

$$ LANGUAGE PLPGSQL SECURITY DEFINER;
