{
  "git.ignoreLimitWarning": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "always",
  },
  "editor.formatOnSave": true,
  "css.customData": [".vscode/tailwind.json"],
  "scss.lint.unknownAtRules": "ignore",
  "css.validate": false,
  "scss.validate": false,
  "search.exclude": {
    "**/yarn.lock": true,
    "**/node_modules": true,
    "**/target": true,
    "**/dist": true,
    "**/build": true,
    "**/coverage": true,
    "**/Cargo.lock": true
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  }
}