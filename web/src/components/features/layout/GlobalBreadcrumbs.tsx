import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Link, useLocation } from "react-router";

export const GlobalBreadcrumbs = () => {
  const location = useLocation();
  const pathnames = location.pathname.split("/").filter(Boolean);

  return (
    <Breadcrumb className="py-6">
      <Breadcrumb.List>
        <Breadcrumb.Item>
          <Breadcrumb.Link asChild>
            <Link to="/">Dashboard</Link>
          </Breadcrumb.Link>
        </Breadcrumb.Item>
        {pathnames.length > 0 && <Breadcrumb.Separator />}
        {pathnames.map((segment, idx) => {
          const to = "/" + pathnames.slice(0, idx + 1).join("/");
          const isLast = idx === pathnames.length - 1;
          const label = segment.replace(/-/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());
          return (
            <span key={`${to}-${idx}`}>
              <Breadcrumb.Item>
                {isLast ? (
                  <Breadcrumb.Page>{label}</Breadcrumb.Page>
                ) : (
                  <Breadcrumb.Link asChild>
                    <Link to={to}>{label}</Link>
                  </Breadcrumb.Link>
                )}
              </Breadcrumb.Item>
              {!isLast && <Breadcrumb.Separator />}
            </span>
          );
        })}
      </Breadcrumb.List>
    </Breadcrumb>
  );
};
