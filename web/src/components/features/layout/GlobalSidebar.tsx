import {
  <PERSON>bar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>barFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarHeader,
} from "@/components/ui/Sidebar";
import { CampaignSidebarSwitcher } from "@/components/features/campaign/CampaignSidebarSwitcher";
import { useCampaignStore, type Campaign } from "@/stores/useCampaignStore";
import { useState } from "react";
import {
  CreateCampaignDialog,
  type CreateCampaignFormData,
} from "@/components/features/campaign/CreateCampaignDialog";
import { Button } from "@/components/ui/Button";
import { useAuth } from "@/hooks/auth/useAuth";
import { WorldbuildingMenu } from "@/components/features/sidebar/WorldbuildingMenu";

export const GlobalSidebar = () => {
  const campaignStore = useCampaignStore.getState();
  const { logout } = useAuth();

  const [createCampaignDialogOpen, setCreatCampaignDialogOpen] = useState(false);

  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(
    campaignStore.selectedCampaign,
  );

  const selectCampaign = (campaign: Campaign) => {
    campaignStore.setCampaign(campaign);
    setSelectedCampaign(campaign);
  };

  const joinCampaign = () => {};

  const createCampaign = async (data: CreateCampaignFormData) => {
    const createdCampaign = await campaignStore.createCampaign(data.name, data.description);
    setCreatCampaignDialogOpen(false);
    if (createdCampaign) {
      selectCampaign(createdCampaign);
    }
  };

  return (
    <>
      <Sidebar>
        <SidebarHeader>
          <CampaignSidebarSwitcher
            campaigns={campaignStore.campaigns}
            selectedCampaign={selectedCampaign}
            selectCampaign={selectCampaign}
            createCampaign={() => setCreatCampaignDialogOpen(true)}
            joinCampaign={joinCampaign}
          />
        </SidebarHeader>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Players</SidebarGroupLabel>
          </SidebarGroup>
          <WorldbuildingMenu />
        </SidebarContent>

        <SidebarFooter>
          <Button onClick={logout}>Sign out</Button>
        </SidebarFooter>
      </Sidebar>

      <CreateCampaignDialog
        onCreate={createCampaign}
        open={createCampaignDialogOpen}
        onOpenChange={setCreatCampaignDialogOpen}
        showTrigger={false}
      />
    </>
  );
};
