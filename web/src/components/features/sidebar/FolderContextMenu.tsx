import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuShortcut,
  ContextMenuTrigger,
} from "@/components/ui/ContextMenu";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
  AlertDialogFooter,
  AlertDialogHeader,
} from "@/components/ui/AlertDialog";
import { Trash } from "lucide-react";
import type { ReactNode } from "react";
import { useState } from "react";
import { useFolderStore } from "@/stores/useFolderStore";

export const FolderContextMenu = ({ children, id }: { children: ReactNode; id: string }) => {
  const [open, setOpen] = useState(false);

  const folderStore = useFolderStore();

  const handleDelete = async () => {
    setOpen(false);
    await folderStore.deleteFolder(id);
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <>
      <ContextMenu>
        <ContextMenuTrigger
          className="flex h-[150px] w-[300px] items-center justify-center rounded-md text-sm"
          onContextMenu={handleContextMenu}
        >
          {children}
        </ContextMenuTrigger>
        <ContextMenuContent className="w-52">
          <ContextMenuItem
            inset
            onPointerDown={(e) => e.stopPropagation()}
            onSelect={() => {
              setOpen(true);
            }}
          >
            Delete folder
            <ContextMenuShortcut>
              <Trash className="text-red-500" />
            </ContextMenuShortcut>
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>

      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogContent onClick={(e) => e.stopPropagation()}>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the folder and all of its
              contents.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              Delete <Trash className="text-red-500 ml-auto" />
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
