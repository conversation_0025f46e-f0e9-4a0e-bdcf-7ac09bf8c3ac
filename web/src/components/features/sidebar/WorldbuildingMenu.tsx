import { Form, FormControl, FormItem, FormField } from "@/components/ui/Form";
import { Input } from "@/components/ui/Input";
import { SidebarGroup, SidebarGroupLabel } from "@/components/ui/Sidebar";
import { useFolderStore } from "@/stores/useFolderStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { Minus, Plus } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import z from "zod";
import { WorldbuildingFolders } from "./WorldbuildingFolders";

const createFolderSchema = z.object({
  name: z.string(),
});

type CreateFolderFormData = z.infer<typeof createFolderSchema>;

export const WorldbuildingMenu = () => {
  const form = useForm<CreateFolderFormData>({
    resolver: zodResolver(createFolderSchema),
    defaultValues: {
      name: "",
    },
  });

  const folderStore = useFolderStore();

  const [showCreateFolderInput, setShowCreateFolderInput] = useState(false);

  useEffect(() => {
    if (showCreateFolderInput) {
      const currentNameInput = document.getElementById("create-folder-name");
      currentNameInput?.focus();
    }
  }, [showCreateFolderInput]);

  const createFolder = async (data: CreateFolderFormData) => {
    if (!data.name.trim()) {
      setShowCreateFolderInput(false);
      return;
    }

    await folderStore.createFolder(data.name.trim());
    form.reset();
    setShowCreateFolderInput(false);
  };

  const folderItems = folderStore.folders.map((folder) => ({
    id: folder.id,
    name: folder.name,
    isActive: false,
    notes: folder.notes,
  }));

  return (
    <SidebarGroup>
      <SidebarGroupLabel>
        Worldbuilding
        {showCreateFolderInput ? (
          <Minus
            className="ml-auto cursor-pointer"
            onClick={() => setShowCreateFolderInput(false)}
          />
        ) : (
          <Plus className="ml-auto cursor-pointer" onClick={() => setShowCreateFolderInput(true)} />
        )}
      </SidebarGroupLabel>
      {showCreateFolderInput && (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(createFolder)}>
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      id="create-folder-name"
                      placeholder=""
                      className="ml-4 mr-2 w-auto"
                      {...field}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </form>
        </Form>
      )}

      <WorldbuildingFolders items={folderItems} />
    </SidebarGroup>
  );
};
