import { Check, ChevronsUpDown, GalleryVerticalEnd, Plus } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/DropdownMenu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/Sidebar";
import type { Campaign } from "@/stores/useCampaignStore";

export function CampaignSidebarSwitcher({
  campaigns,
  selectedCampaign,
  selectCampaign,
  createCampaign,
  joinCampaign,
}: {
  campaigns: Campaign[];
  selectedCampaign: Campaign | null;
  selectCampaign: (campaign: Campaign) => void;
  createCampaign: () => void;
  joinCampaign: () => void;
}) {
  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <GalleryVerticalEnd className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="truncate font-medium">{selectedCampaign?.name}</span>
                <span className="truncate text-xs">{selectedCampaign?.role}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-(--radix-dropdown-menu-trigger-width)" align="start">
            {campaigns.map((campaign) => (
              <DropdownMenuItem key={campaign.id} onSelect={() => selectCampaign(campaign)}>
                {campaign.name}{" "}
                {campaign.id === selectedCampaign?.id && <Check className="ml-auto" />}
              </DropdownMenuItem>
            ))}
            <hr className="m-2" />
            <DropdownMenuItem onSelect={createCampaign}>
              Create new campaign <Plus className="ml-auto" />
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={joinCampaign}>
              Join campaign <Plus className="ml-auto" />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
