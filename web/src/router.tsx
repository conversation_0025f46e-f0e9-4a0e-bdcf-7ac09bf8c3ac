import { createBrowserRouter } from "react-router";
import { Login } from "./pages/Login";
import { Dashboard } from "./pages/Dashboard";
import { useRequireAuth } from "./loaders/useRequireAuth";
import { DefaultLayout } from "./layouts/DefaultLayout";
import { useRequireLogin } from "./loaders/useRequireLogin";
import { CampaignSelectorLayout } from "./layouts/CampaignSelectorLayout";
import { SwitchCampaign } from "./pages/SwitchCampaign";
import { useRequireCampaign } from "./loaders/useRequireCampaign";
import { Notes } from "./pages/Notes";

export const router = createBrowserRouter([
  {
    path: "login",
    element: <Login />,
    loader: useRequireLogin,
  },
  {
    element: <CampaignSelectorLayout />,
    loader: useRequireAuth,
    children: [
      {
        path: "select-campaign",
        element: <SwitchCampaign />,
      },
    ],
  },
  {
    element: <DefaultLayout />,
    loader: useRequireCampaign,
    children: [
      { index: true, element: <Dashboard /> },
      {
        path: "notes",
        element: <Notes />,
      },
    ],
  },
]);
