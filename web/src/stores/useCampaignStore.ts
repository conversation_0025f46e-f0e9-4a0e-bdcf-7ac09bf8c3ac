import { create } from "zustand";
import { persist } from "zustand/middleware";
import { supabase } from "@/lib/supabase";
import { type Tables } from "@dnd-cms/supabase/database.types";

export enum CampaignRole {
  DM = "DM",
  Player = "Player",
}

export type Campaign = Tables<"t_campaign"> & {
  role: CampaignRole;
};

interface CampaignState {
  selectedCampaign: Campaign | null;
  setCampaign: (campaign: Campaign | null) => void;
  campaigns: Campaign[];
  setCampaigns: (campaigns: Campaign[]) => void;
  fetchUserCampaigns: ({ campaignId }: { campaignId?: string }) => Promise<Campaign[] | []>;
  createCampaign: (name: string, description: string) => Promise<Campaign | null>;
}

export const useCampaignStore = create<CampaignState>()(
  persist(
    (set, get) => ({
      selectedCampaign: null,
      setCampaign: (campaign) => set({ selectedCampaign: campaign }),
      campaigns: [],
      setCampaigns: (campaigns) => set({ campaigns }),
      fetchUserCampaigns: async ({ campaignId }: { campaignId?: string }) => {
        try {
          const {
            data: { session },
          } = await supabase.auth.getSession();
          const user = session?.user;

          if (!user) {
            throw new Error("No authenticated user found");
          }

          const { data, error } = await supabase.rpc("sp_fetch_user_campaigns", {
            _user_id: user.id,
            _campaign_id: campaignId,
          });

          if (error) {
            throw error;
          }

          if (!data) {
            set({ campaigns: [] });
            return [];
          }

          const campaigns = data.map((row: Campaign) => row).filter(Boolean) as Campaign[];

          if (!campaigns.length) {
            set({ campaigns: [] });
            return [];
          }

          set({ campaigns });
          return campaigns;
        } catch (error) {
          console.error("Error fetching campaigns:", error);
          set({ campaigns: [] });
          return [];
        }
      },
      createCampaign: async (name: string, description: string) => {
        try {
          const {
            data: { session },
          } = await supabase.auth.getSession();
          const user = session?.user;

          if (!user) {
            throw new Error("No authenticated user found");
          }

          const { data, error } = await supabase.rpc("sp_create_campaign", {
            _name: name,
            _description: description,
            _created_by: user.id,
          });

          if (error) {
            throw error;
          }

          if (!data || data.length === 0) {
            return null;
          }

          const campaignId = data[0].id;
          const campaigns: Campaign[] = await get().fetchUserCampaigns({});
          return campaigns.find((c) => c.id === campaignId) || null;
        } catch (error) {
          console.error("Error creating campaign:", error);
          return null;
        }
      },
    }),
    {
      name: "campaign-store",
    },
  ),
);
