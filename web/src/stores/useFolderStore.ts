import { create } from "zustand";
import { persist } from "zustand/middleware";
import { supabase } from "@/lib/supabase";
import type { Tables } from "@dnd-cms/supabase/database.types";
import { useCampaignStore } from "./useCampaignStore";

export type Note = Tables<"t_note">;

export type Folder = Tables<"t_folder"> & {
  notes: Note[];
};

interface FolderState {
  folders: Folder[];
  fetchFolders: ({ includeDeleted }: { includeDeleted?: boolean }) => Promise<Folder[] | []>;
  createFolder: (name: string) => Promise<Folder | null>;
  deleteFolder: (id: string) => Promise<void>;
  createNote: ({
    title,
    content,
    folderId,
  }: {
    title: string;
    content?: string;
    folderId: string;
  }) => Promise<Note | undefined>;
  recentNotes: Note[];
  openNote: (noteId: string) => void;
  closeNote: (noteId: string) => void;
  activeNote: Note | undefined;
  setActiveNote: (noteId: string) => void;
}

export const useFolderStore = create<FolderState>()(
  persist(
    (set, get) => ({
      folders: [],
      recentNotes: [],
      activeNote: undefined,
      fetchFolders: async ({ includeDeleted = false }) => {
        try {
          const campaignstore = useCampaignStore.getState();

          if (!campaignstore.selectedCampaign) {
            throw new Error("No campaign selected");
          }

          const { data, error } = await supabase.rpc("sp_fetch_folders", {
            _campaign_id: campaignstore.selectedCampaign.id,
            _include_deleted: includeDeleted,
          });

          if (error) {
            throw error;
          }

          if (!data) {
            set({ folders: [] });
            return [];
          }

          const folders = data.map((row: Folder) => row).filter(Boolean) as Folder[];
          set({ folders });
          return folders;
        } catch (error) {
          console.error("Error fetching folders:", error);
          return [];
        }
      },
      createFolder: async (name: string) => {
        try {
          const {
            data: { session },
          } = await supabase.auth.getSession();
          const user = session?.user;

          if (!user) {
            throw new Error("No authenticated user found");
          }

          const campaignstore = useCampaignStore.getState();

          if (!campaignstore.selectedCampaign) {
            throw new Error("No campaign selected");
          }

          const { data, error } = await supabase
            .from("t_folder")
            .insert([{ name, created_by: user.id, campaign_id: campaignstore.selectedCampaign.id }])
            .select()
            .single();

          if (error) {
            throw error;
          }

          if (!data) {
            return null;
          }

          await get().fetchFolders({});

          return data;
        } catch (error) {
          console.error("Error creating folder:", error);
          return null;
        }
      },
      deleteFolder: async (id: string) => {
        try {
          const { error } = await supabase
            .from("t_folder")
            .update({ is_deleted: true })
            .eq("id", id);

          if (error) {
            throw error;
          }

          await get().fetchFolders({});
        } catch (error) {
          console.error("Error deleting folder:", error);
        }
      },
      createNote: async ({ title, content, folderId }) => {
        try {
          const {
            data: { session },
          } = await supabase.auth.getSession();
          const user = session?.user;

          if (!user) {
            throw new Error("No authenticated user found");
          }

          const campaignstore = useCampaignStore.getState();

          if (!campaignstore.selectedCampaign) {
            throw new Error("No campaign selected");
          }

          const { data, error } = await supabase
            .from("t_note")
            .insert([
              {
                title,
                content,
                folder_id: folderId,
                created_by: user.id,
                campaign_id: campaignstore.selectedCampaign.id,
              },
            ])
            .select()
            .single();

          if (error) {
            throw error;
          }

          if (!data) {
            return undefined;
          }

          await get().fetchFolders({});

          return data as Note;
        } catch (error) {
          console.error("Error creating note:", error);
          return undefined;
        }
      },
      openNote: (noteId: string) => {
        const folder = get().folders.find((folder) =>
          folder.notes.some((note) => note.id === noteId),
        );
        if (!folder) return;

        const note = folder.notes.find((note) => note.id === noteId);
        if (!note) return;

        set((state) => {
          const updatedRecentNotes = [note!, ...state.recentNotes.filter((n) => n.id !== noteId)];

          return { recentNotes: updatedRecentNotes, activeNote: note };
        });
      },
      closeNote: (noteId: string) => {
        set((state) => ({
          recentNotes: state.recentNotes.filter((note) => note.id !== noteId),
          activeNote: state.activeNote?.id === noteId ? undefined : state.activeNote,
        }));
      },
      setActiveNote: (noteId: string) => {
        const note = get().recentNotes.find((note) => note.id === noteId);
        if (note) {
          set({ activeNote: note });
        }
      },
    }),
    {
      name: "folder-store",
    },
  ),
);
