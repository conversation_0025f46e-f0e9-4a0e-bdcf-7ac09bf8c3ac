import {
  CreateCampaignDialog,
  type CreateCampaignFormData,
} from "@/components/features/campaign/CreateCampaignDialog";
import { Button } from "@/components/ui/Button";
import { Table } from "@/components/ui/Table";
import { useCampaignStore, type Campaign } from "@/stores/useCampaignStore";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router";

export const SwitchCampaign = () => {
  const campaignStore = useCampaignStore();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    campaignStore.fetchUserCampaigns({});

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const selectCampaign = (campaign: Campaign) => {
    campaignStore.setCampaign(campaign);
    navigate("/");
  };

  const joinCampaign = () => {
    // Logic to join an existing campaign
  };

  const createCampaign = async (data: CreateCampaignFormData) => {
    await campaignStore.createCampaign(data.name, data.description);
    setCreateDialogOpen(false);
  };

  return (
    <>
      <div className="w-xl">
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.Head>Campaign Name</Table.Head>
              <Table.Head>Description</Table.Head>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {campaignStore.campaigns.map((campaign) => (
              <Table.Row
                key={campaign.id}
                className="cursor-pointer"
                onClick={() => selectCampaign(campaign)}
              >
                <Table.Cell>{campaign.name}</Table.Cell>
                <Table.Cell>{campaign.description || "No description"}</Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>

        <div className="mt-4 flex space-x-2 w-full justify-center">
          <Button variant="secondary" onClick={joinCampaign}>
            Join a campaign
          </Button>
          <CreateCampaignDialog
            onCreate={createCampaign}
            open={createDialogOpen}
            onOpenChange={setCreateDialogOpen}
          />
        </div>
      </div>
    </>
  );
};
