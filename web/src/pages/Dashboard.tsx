import { useCampaignStore } from "@/stores/useCampaignStore";
import { useFolderStore } from "@/stores/useFolderStore";
import { useEffect } from "react";

export const Dashboard = () => {
  const campaignStore = useCampaignStore();
  const folderStore = useFolderStore();

  useEffect(() => {
    campaignStore.fetchUserCampaigns({});
    folderStore.fetchFolders({});

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  console.log(campaignStore.selectedCampaign);

  return (
    <div>
      <h1>Dashboard</h1>
    </div>
  );
};
