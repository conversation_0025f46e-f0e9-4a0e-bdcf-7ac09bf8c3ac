import { handleRequireAuth } from "./useRequireAuth";
import { redirect } from "react-router";
import { useCampaignStore } from "@/stores/useCampaignStore";

export const useRequireCampaign = async () => {
  await handleRequireAuth();

  const store = useCampaignStore.getState();

  if (!store.selectedCampaign) {
    throw redirect("/select-campaign");
  }

  console.log("Current path:", window.location.pathname);

  return null;
};
