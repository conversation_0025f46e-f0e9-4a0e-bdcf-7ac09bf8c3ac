import { useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { generateNonce } from "@/utils/auth/auth";

declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    google?: any;
  }
}

export function useGoogleAuth() {
  useEffect(() => {
    let cancelled = false;

    async function init() {
      const [nonce, hashedNonce] = await generateNonce();

      if (!window.google?.accounts?.id) {
        console.warn("Google One Tap SDK not loaded. Did you include the <script>?");
        return;
      }

      window.google.accounts.id.initialize({
        client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        callback: async (response: any) => {
          try {
            if (cancelled) return;

            const { error } = await supabase.auth.signInWithIdToken({
              provider: "google",
              token: response.credential,
              nonce,
              options: {
                // @ts-expect-error supabase supports this
                redirectTo: `${window.location.origin}/auth/callback`,
              },
            });

            if (error) throw error;
          } catch (err) {
            console.error("Google One Tap error:", err);
          }
        },
        nonce: hashedNonce,
        use_fedcm_for_prompt: false,
        auto_select: false,
        cancel_on_tap_outside: true,
      });

      const buttonElement = document.getElementById("g_id_signin");
      if (buttonElement) {
        window.google.accounts.id.renderButton(buttonElement, {
          type: "standard",
          theme: "outline",
          size: "large",
          text: "sign_in_with",
          shape: "rectangular",
          logo_alignment: "left",
          width: 300,
        });
      }

      setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.google.accounts.id.prompt((notification: any) => {
          if (notification.isNotDisplayed()) {
            console.log("Prompt not displayed:", notification.getNotDisplayedReason());
          } else if (notification.isSkippedMoment()) {
            console.log("Prompt skipped:", notification.getSkippedReason());
          } else if (notification.isDismissedMoment()) {
            console.log("Prompt dismissed:", notification.getDismissedReason());
          }
        });
      }, 100);
    }

    init();

    return () => {
      cancelled = true;
    };
  }, []);
}
