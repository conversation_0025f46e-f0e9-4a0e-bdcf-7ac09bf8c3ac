{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": false, "tsx": true, "tailwind": {"config": "", "css": "src/index.css", "baseColor": "zinc", "cssVariables": true, "prefix": ""}, "iconLibrary": "lucide", "aliases": {"components": "@/components", "utils": "@/utils/shadcn/utils", "ui": "@/components/ui", "lib": "@/lib", "hooks": "@/hooks"}, "registries": {"@shadcn-editor": "https://shadcn-editor.vercel.app/r/{name}.json"}}